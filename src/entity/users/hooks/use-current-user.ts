'use client';

import {User} from "@/entity/users/model";
import {useContext} from "react";
import {CurrentUserContext} from "@/entity/users/ui/current-user";

export const useCurrentUser = (): User => {
    const currentUser = useContext(CurrentUserContext)

    console.log('current user ', currentUser)

    if(!currentUser){
        throw new Error('Current user topilmadi!')
    }

    return currentUser
}