'use server';

import { dbConnect } from "@/shared/lib/database";
import { VotesSchema } from "@/entity/votes/schema";

export type AddPhoneParams = {
    voteId: string; // ID существующего голоса для обновления
    phoneNumber: string;
    originalPhoneNumber?: string; // Оригинальный номер телефона (короткий)
    ownerId?: string; // ID хозяйства (необязательное поле)
    updatedBy?: string; // Кто обновляет данные (имя пользователя или ID)
};

/**
 * Обновляет существующий голос, добавляя номер телефона и привязывая к хозяйству (опционально)
 */
export async function addPhone(params: AddPhoneParams) {
    try {
        await dbConnect();

        const { phoneNumber, originalPhoneNumber, ownerId, voteId, updatedBy } = params;

        // Создаем объект с данными для обновления
        const updateData: any = {
            phoneNumber
        };

        // Если передан originalPhoneNumber, добавляем его в данные для обновления
        if (originalPhoneNumber) {
            updateData.originalPhoneNumber = originalPhoneNumber;
        }

        // Если передан ownerId, добавляем его в данные для обновления
        if (ownerId) {
            updateData.ownerId = ownerId;
        }

        // Если передан updatedBy, добавляем его в данные для обновления
        if (updatedBy) {
            updateData.updatedBy = updatedBy;
        }

        // Обновляем существующий голос в базе данных
        const vote = await VotesSchema.findByIdAndUpdate(
            voteId,
            { $set: updateData },
            { new: true } // Возвращает обновленный документ
        );

        if (!vote) {
            return {
                success: false,
                error: 'Vote not found'
            };
        }

        return {
            success: true,
            data: {
                _id: vote._id.toString(),
                phoneNumber: vote.phoneNumber,
                originalPhoneNumber: vote.originalPhoneNumber,
                imageKey: vote.imageKey,
                voteDate: vote.voteDate,
                ownerId: vote.ownerId ? vote.ownerId.toString() : undefined,
                updatedBy: vote.updatedBy,
                createdAt: vote.createdAt.toISOString(),
                updatedAt: vote.updatedAt.toISOString(),
            }
        };
    } catch (error) {
        console.error('Error adding phone:', error);
        return {
            success: false,
            error: (error as Error).message || 'Failed to add phone'
        };
    }
}
